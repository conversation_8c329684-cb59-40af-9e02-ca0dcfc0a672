import React, { useState, useEffect, useCallback } from "react";
import { Mo<PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Switch from "react-switch";
import API from "../services/API";
import { AIR_MEDICAL_WITH_ID_URL, AIR_WITH_ID_URL, ALL_LOCATIONTHREE_LOCATIONFOUR_URL, PPES_URL, INJURY_URL, GENERAL_USER_URL, EQUIPMENT_CATEGORIES_URL, GHS_ONE_URL, GHS_TWO_URL, LIGHTING_URL, LOCATION3_URL, SURFACE_CONDITION_URL, SURFACE_TYPE_URL, TIER2_TIER3_URL, WEATHER_CONDITION_URL, WORKING_GROUP_URL, WORK_ACTIVITIES_URL } from "../constants";
import IncidentStory from "./IncidentStory";
import cogoToast from "cogo-toast";
import { DropzoneArea } from 'material-ui-dropzone';
import { BodyComponent } from "reactjs-human-body";
import Select from "react-select";
import DatePicker from "react-datepicker";
import moment from 'moment';
import "react-datepicker/dist/react-datepicker.css";


// Import required date-fns functions if needed for formatting
import { format } from 'date-fns';

const AirMedicalCard = ({ showModal, setShowModal, data }) => {


    function initializeDateWithOffset(date) {
        return moment(date).utcOffset('+0530');
    }
    const [generalUsers, setGeneralUsers] = useState([]);
    const [injuries, setInjuries] = useState([]);
    const [formData, setFormData] = useState({
        description: '',
        timeOfFirstAid: initializeDateWithOffset(new Date()),
        vmo: false,
        ambulance: false,
        nhs: false,
        departureFromTerminal: initializeDateWithOffset(new Date()),
        admissionAtNhs: initializeDateWithOffset(new Date()),
        treatment: '',
        otherComments: ''
    });

    const handleChange = (e) => {
        console.log(e.target.name)
        const { name, value, type, checked } = e.target;
        setFormData({
            ...formData,
            [name]: type === 'checkbox' ? checked : value
        });
    }

    const handleDateChange = (name, date) => {
        setFormData({
            ...formData,
            [name]: moment(date).utcOffset('+0530')
        });
    }
    const handleSwitchChange = (property, value) => {
        setFormData({
            ...formData,
            [property]: value
        });
    }

    const getGeneralUsers = useCallback(async () => {
        const response = await API.get(GENERAL_USER_URL);
        if (response.status === 200) {
            setGeneralUsers(response.data);
        }
    }, []);



    const getInjuries = useCallback(async () => {
        const response = await API.get(INJURY_URL);
        if (response.status === 200) {
            setInjuries(response.data.map(option => ({ value: option.id, label: option.name })));
        }
    }, []);









    const [witnessInvolved, setWitnessInvolved] = useState({
        witnessInvolved: [
            {
                "internal": true,
                "selectedEmp": {

                },
                "name": "",
                "empId": "",
                "designation": "",
                "comments": ""
            }
        ],
        personInvolved: [
            {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": {
                },
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": []
            }
        ],
        personnelImpacted: [
            {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": {
                },
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": []
            }
        ]

    })





    const handlePersonImpactedChange = (index, field, value, flag) => {
        let newValue = value;

        if (flag && field === 'selectedEmp') {
            const data = generalUsers.find(i => i.id === value)

            newValue = {
                "name": "",
                "empId": "",
                "designation": "",
                "comments": "",
                "internal": true,
                "selectedEmp": data,
                "injured": false,
                "injuryParts": [],
                "injuryDetails": [],
                "isPPE": false,
                "ppes": []
            }
            setWitnessInvolved(prevState => {
                const updatedActions = [...prevState.personInvolved];
                updatedActions[index] = newValue;

                return { ...prevState, personInvolved: updatedActions };
            });
        } else {
            setWitnessInvolved(prevState => {
                const updatedActions = [...prevState.personInvolved];
                updatedActions[index][field] = newValue;

                return { ...prevState, personInvolved: updatedActions };
            });
        }

    };





    // Effects
    useEffect(() => {

        getGeneralUsers();

        getInjuries();
    }, [getGeneralUsers, getInjuries]);

    useEffect(() => {
        if (data) {

            const updatedPersonInvolved = data.personInvolved.map(person => ({
                ...person,
                medicalOfficerSurveillance: '', // Ensure this field is added
            }));

            // Similarly for personnelImpacted
            const updatedPersonnelImpacted = data.personnelImpacted.map(person => ({
                ...person,
                medicalOfficerSurveillance: '', // Ensure this field is added
            }));

            setWitnessInvolved({ witnessInvolved: data.witnessInvolved, personnelImpacted: updatedPersonnelImpacted, personInvolved: updatedPersonInvolved })

        }
    }, [data]);



    // Handling form submission

    const reverseToMobileFormatBodyParts = obj => Object.keys(obj).map(key =>
        key.split('_')
            .map((word, index) => index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1))
            .map(word => word === 'left' ? 'right' : word === 'right' ? 'left' : word)
            .join('')
    );




    const [files, setFiles] = useState([]);

    const handleFileChange = (file) => {
        setFiles(file)

    }


    const [allComments, setAllComments] = useState(
        data.personInvolved.map(person => ({ name: person.name, comments: '' }))
    );

    const handleCommentChange = (name, value) => {
        setAllComments(prevComments => {
            return prevComments.map(comment => {
                if (comment.name === name) {
                    return { ...comment, comments: value };
                } else {
                    return comment;
                }
            });
        });
    }





    const [isClicked, setIsClicked] = useState(false);
    const handleSubmit = async () => {
        setIsClicked(true);
        try {
            // Patch Request to AIR_WITH_ID_URL
            const response = await API.patch(AIR_MEDICAL_WITH_ID_URL(data.id, data.actionId), {
                medicalReport: formData,
                witnessInvolved: witnessInvolved.witnessInvolved,
                personInvolved: witnessInvolved.personInvolved,
                personnelImpacted: witnessInvolved.personnelImpacted
            });

            // If the patch request fails, no need to proceed further
            if (response.status !== 204) {
                setIsClicked(false);
                console.error('Failed to patch data. Status:', response.status);
                return;  // or handle this error appropriately
            }

            // Sending POST requests for each driver using for...of loop to ensure each request completes before the next

            cogoToast.success(`Action for IR ${data.maskId} Completed`)
            // Proceed to the next step
            setShowModal(false)
            setIsClicked(false);

        } catch (error) {
            console.error('An error occurred:', error);
            setIsClicked(false);

        }
        setIsClicked(false);
    };

    const displayText = (label, text) => (
        <div className="mb-3">
            <strong>{label}: </strong> {text || "N/A"}
        </div>
    );

    const handleMedicalOfficerSurveillanceChange = (index, value, type) => {
        setWitnessInvolved(prevState => {
            const updatedList = prevState[type].map((item, i) => {
                if (i === index) {
                    return { ...item, medicalOfficerSurveillance: value };
                }
                return item;
            });

            return { ...prevState, [type]: updatedList };
        });
    };
    const renderPersonFields = (action, index, type) => {
        const isInternal = action.internal;
        const isPersonInjured = action.injured;

        const commonFields = isPersonInjured ? (
            <>
                <BodyComponent onChange={(value) => handlePersonImpactedChange(index, "injuryParts", reverseToMobileFormatBodyParts(value), true)} partsInput={action.injuryParts.reduce((acc, part) => {
                    let modifiedPart = part
                        .replace(/^left/i, 'TEMP')
                        .replace(/^right/i, 'left')
                        .replace(/^TEMP/i, 'right');
                    modifiedPart = modifiedPart.replace(/([A-Z])/g, "_$1").toLowerCase();
                    acc[modifiedPart] = { selected: true };
                    return acc;
                }, {})} />

                <label>More Details on Injury</label>

                <Select
                    isMulti
                    options={injuries}
                    value={action.injuryDetails
                        ? action.injuryDetails.filter(option => option !== null && option !== undefined).map(option => ({
                            value: option.id,
                            label: option.name,
                        }))
                        : []
                    }
                    onChange={(selected) => handlePersonImpactedChange(index, "injuryDetails", selected.map(option => ({
                        id: option.value,
                        name: option.label,
                    })), true)}
                    classNamePrefix="react-select"
                />
                <div>
                    <label>Medical Officer Surveillance/ Observation</label>
                    <textarea
                        className="form-control"
                        value={action.medicalOfficerSurveillance || ''} // Use the value from the current action item
                        onChange={e => handleMedicalOfficerSurveillanceChange(index, e.target.value, type)} // type can be "personInvolved" or "personnelImpacted"
                    ></textarea>
                </div>

            </>
        ) : null;

        if (isInternal) {

            // const selectedUser = generalUsers.length > 0 ? generalUsers.find(user => user.id === action?.selectedEmp?.id) : {};
            return (
                <div className="mb-4">
                    {displayText("Person Involved", action?.selectedEmp?.name ?? '')}
                    {commonFields}
                </div>
            );
        } else {
            return (
                <div className="mb-4">
                    {displayText("NIC", action.empId)}
                    {displayText("Name", action.name)}
                    {displayText("Remarks / Comments", action.comments)}
                    {commonFields}
                </div>
            );
        }
    };




    return (
        <>
            {data && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    <div className="w-100 d-flex align-items-center justify-content-between">
                        <h4 >
                            IR Information - {data.maskId}
                        </h4 >
                        <h4 >
                            Incident Date & Time: {data.incidentDate}
                        </h4 >
                    </div>

                </Modal.Header>

                <Modal.Body>
                    <div className="row">
                        {/* <div className="col-md-6">
                            <IncidentStory data={data} />
                        </div> */}

                        <Form>
                            <div className="row">
                                <Form.Label> Incident Description</Form.Label>
                                <p>{data.description}</p>
                            </div>

                            <div className="row">
                                <div>
                                    <Form.Label> Person Involved </Form.Label>
                                    {witnessInvolved.personInvolved.filter(i => i).map((action, index) =>
                                        <div className="form-group" key={index}>
                                            {renderPersonFields(action, index, "personInvolved")}
                                            <br />
                                        </div>
                                    )}
                                </div>
                                <div>
                                    <Form.Label> Personnel Injured </Form.Label>
                                    {witnessInvolved.personnelImpacted.filter(i => i).map((action, index) =>
                                        <div className="form-group" key={index}>
                                            {renderPersonFields(action, index, "personnelImpacted")}
                                            <br />
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="row">
                                <div className="col-12 mt-3">
                                    <Form.Group controlId="description">
                                        <Form.Label>Overall Summary</Form.Label>
                                        <textarea value={formData.description} name="description" className="form-control" onChange={handleChange}>

                                        </textarea>
                                    </Form.Group>
                                </div>
                                <div className="col-12 mt-3">
                                    <label>Time of First Aid given</label>
                                    <DatePicker
                                        selected={formData.timeOfFirstAid.toDate()}
                                        onChange={(date) => handleDateChange('timeOfFirstAid', date)}
                                        showTimeSelect
                                        timeFormat="HH:mm"
                                        timeIntervals={15}
                                        timeCaption="Time"
                                        dateFormat="dd/MM/yyyy H:mm"
                                        className="form-control w-25"
                                        maxDate={new Date()} // This line disables future dates
                                    />

                                </div>
                                <div className="col-12 mt-3">

                                    <label className="d-flex align-items-center">
                                        If referred to the VMO
                                        <Switch
                                            className="ms-2"
                                            onChange={(e) => handleSwitchChange('vmo', e)}
                                            checked={formData.vmo}
                                        />
                                    </label>

                                </div>

                                <div className="col-12 mt-3">

                                    <label className="d-flex align-items-center">
                                        If facilitated with ambulance
                                        <Switch
                                            className="ms-2"
                                            onChange={(e) => handleSwitchChange('ambulance', e)}
                                            checked={formData.ambulance}
                                        />
                                    </label>

                                </div>

                                <div className="col-12 mt-3">

                                    <label className="d-flex align-items-center">
                                        If admitted to NHSL
                                        <Switch
                                            className="ms-2"
                                            onChange={(e) => handleSwitchChange('nhs', e)}
                                            checked={formData.nhs}
                                        />
                                    </label>

                                </div>
                                <div className="col-12 mt-3">
                                    <label>Time of departure from the terminal</label>
                                    <DatePicker
                                        selected={formData.departureFromTerminal.toDate()}
                                        onChange={(date) => handleDateChange('departureFromTerminal', date)}
                                        showTimeSelect
                                        timeFormat="HH:mm"
                                        timeIntervals={15}
                                        timeCaption="Time"
                                        dateFormat="dd/MM/yyyy H:mm"
                                        className="form-control w-25"
                                        maxDate={new Date()} // This line disables future dates
                                    />

                                </div>

                                <div className="col-12 mt-3">
                                    <label>Time of admission at the NHSL</label>
                                    <DatePicker
                                        selected={formData.admissionAtNhs.toDate()}
                                        onChange={(date) => handleDateChange('admissionAtNhs', date)}
                                        showTimeSelect
                                        timeFormat="HH:mm"
                                        timeIntervals={15}
                                        timeCaption="Time"
                                        dateFormat="dd/MM/yyyy H:mm"
                                        className="form-control w-25"
                                        maxDate={new Date()} // Disables future dates
                                    />
                                </div>
                                <div className="col-12 mt-3">
                                    <Form.Group controlId="description">
                                        <Form.Label>Recommended treatment at NHSL</Form.Label>
                                        <textarea value={formData.treatment} name="treatment" className="form-control" onChange={handleChange}>

                                        </textarea>
                                    </Form.Group>
                                </div>

                                <div className="col-12 mt-3">
                                    <Form.Group controlId="description">
                                        <Form.Label>Other Comments If Any</Form.Label>
                                        <textarea value={formData.otherComments} name="otherComments" className="form-control" onChange={handleChange}>

                                        </textarea>
                                    </Form.Group>
                                </div>

                            </div>


                            <Button variant="primary" onClick={handleSubmit} disabled={isClicked}>Submit</Button>

                        </Form>
                    </div>



                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <Button variant="light" onClick={() => { setShowModal(false); }}>Close</Button>

                </Modal.Footer>
            </Modal>}
        </>
    )
}

export default AirMedicalCard;